const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const express = require('express');
const https = require('https');
const socketIo = require('socket.io');
const { v4: uuidv4 } = require('uuid');
const { Client } = require('@microsoft/microsoft-graph-client');
const { ClientSecretCredential } = require('@azure/identity');
const { TokenCredentialAuthenticationProvider } = require('@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials');
const { BotFrameworkAdapter, TeamsActivityHandler } = require('botbuilder');
const fsPromises = require('fs').promises;
const ffmpeg = require('fluent-ffmpeg');
const { Readable } = require('stream');

// Environment setup
const env = process.argv[2] || 'development';
const envFile = env === 'production' ? '.env.production' : '.env.development';
const envPath = path.resolve(__dirname, '..', envFile);

const result = dotenv.config({ path: envPath });
if (result.error) {
    console.error(`Failed to load environment file: ${envPath}`);
    process.exit(1);
}

ffmpeg.setFfmpegPath('/usr/bin/ffmpeg');

const app = express();

// Configuration
const config = {
    tenantId: process.env.TENANT_ID,
    clientId: process.env.CLIENT_ID,
    clientSecret: process.env.CLIENT_SECRET,
    botId: process.env.BOT_ID,
    botPassword: process.env.BOT_PASSWORD,
    port: process.env.PORT || 3000,
    allowedOrigins: [
        'https://dev-cvm.citrusinformatics.com',
        'https://cvm.citrusinformatics.com',
        'https://teams.microsoft.com'
    ]
};

// Microsoft Graph setup
const credential = new ClientSecretCredential(config.tenantId, config.clientId, config.clientSecret);
const appGraphClient = Client.initWithMiddleware({
    authProvider: new TokenCredentialAuthenticationProvider(credential, {
        scopes: ['https://graph.microsoft.com/.default']
    })
});

// HTTPS server setup
const certKeyPath = path.join(__dirname, '../cert.key');
const certCrtPath = path.join(__dirname, '../cert.crt');

if (!fs.existsSync(certKeyPath) || !fs.existsSync(certCrtPath)) {
    console.error('Certificate files missing:', { certKeyPath, certCrtPath });
    process.exit(1);
}

const httpsOptions = {
    key: fs.readFileSync(certKeyPath),
    cert: fs.readFileSync(certCrtPath)
};

const server = https.createServer(httpsOptions, app);
const io = socketIo(server, {
    path: '/socket.io',
    cors: {
        origin: config.allowedOrigins,
        methods: ['GET', 'POST'],
        allowedHeaders: ['Authorization', 'Content-Type', 'X-Requested-With'],
        credentials: true
    },
    transports: ['websocket', 'polling'],
    pingTimeout: 300000,
    pingInterval: 25000,
    maxHttpBufferSize: 5e6,
    connectTimeout: 60000
});

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
    const origin = req.headers.origin;
    if (config.allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
    }
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    next();
});

app.use(express.static(path.join(__dirname, '../public'), { index: 'index.html' }));

// Authentication middleware
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader?.split(' ')[1];
        
        if (!token) {
            return res.status(401).json({
                error: 'Access denied',
                message: 'Authorization token required'
            });
        }
        
        const testGraphClient = Client.init({
            authProvider: (done) => done(null, token)
        });
        
        const userProfile = await testGraphClient.api('/me').get();
        req.user = {
            id: userProfile.id,
            userPrincipalName: userProfile.userPrincipalName,
            displayName: userProfile.displayName
        };
        req.accessToken = token;
        next();
        
    } catch (error) {
        const statusCode = error.statusCode === 401 ? 403 : 500;
        const message = error.statusCode === 401 ? 
            'The provided authorization token is invalid or expired' : 
            'Failed to validate authorization token';
        
        res.status(statusCode).json({
            error: error.statusCode === 401 ? 'Invalid token' : 'Authentication error',
            message
        });
    }
};

// Input validation middleware
const validateInput = (requiredFields = []) => {
    return (req, res, next) => {
        const missingFields = requiredFields.filter(field => !req.body[field]);
        if (missingFields.length > 0) {
            return res.status(400).json({
                error: 'Bad request',
                message: `Missing required fields: ${missingFields.join(', ')}`
            });
        }
        next();
    };
};

// Bot Framework
class MyBot extends TeamsActivityHandler {
    async handleTeamsMessagingExtensionFetchTask(_context) {
        return {
            task: {
                type: 'continue',
                value: {
                    title: 'Record Voice Message',
                    height: 380,
                    width: 350,
                    url: 'https://dev-cvm.citrusinformatics.com/index.html',
                    fallbackUrl: 'https://dev-cvm.citrusinformatics.com/index.html'
                }
            }
        };
    }

    async handleTeamsMessagingExtensionSubmitAction(_context, action) {
        try {
            if (action.data?.error) {
                throw new Error(`Submit action failed: ${action.data.error}`);
            }
            
            if (action.data?.status === 'sending') {
                return {
                    status: 200,
                    body: {
                        composeExtension: {
                            type: 'message',
                            text: 'Processing voice message...'
                        }
                    }
                };
            }

            const responseContent = {
                title: 'Voice Message Sent',
                subtitle: action.data?.downloadUrl ? 
                    'Your voice message has been sent successfully.' : 
                    'Your voice message is being processed and will be delivered shortly.',
                text: action.data?.downloadUrl ? 'Message delivered!' : 'Processing in background...'
            };
            
            if (action.data?.downloadUrl) {
                responseContent.buttons = [{
                    type: 'openUrl',
                    title: 'Listen',
                    value: action.data.downloadUrl
                }];
            }

            return {
                status: 200,
                body: {
                    composeExtension: {
                        type: 'result',
                        attachmentLayout: 'list',
                        attachments: [{
                            contentType: 'application/vnd.microsoft.card.thumbnail',
                            content: responseContent
                        }]
                    }
                }
            };
        } catch (error) {
            return {
                status: 200,
                body: {
                    composeExtension: {
                        type: 'result',
                        attachmentLayout: 'list',
                        attachments: [{
                            contentType: 'application/vnd.microsoft.card.thumbnail',
                            content: {
                                title: 'Voice Message Sent',
                                subtitle: 'Your voice message has been sent successfully.',
                                text: 'Message delivered!'
                            }
                        }]
                    }
                }
            };
        }
    }
}

const botAdapter = new BotFrameworkAdapter({
    appId: config.botId,
    appPassword: config.botPassword
});

const bot = new MyBot();

botAdapter.onTurnError = async (context, error) => {
    console.error('Bot error:', error);
    await context.sendActivity('Sorry, there was an error processing your request.');
};

// Routes
app.get('/', (_req, res) => res.redirect('/index.html'));

app.post('/api/messages', async (req, res) => {
    try {
        await botAdapter.processActivity(req, res, async (context) => {
            await bot.run(context);
        });
    } catch (error) {
        console.error('Bot processing error:', error);
        res.status(500).send({ error: 'Bot processing failed' });
    }
});

// API Endpoints
app.post('/api/get-access-token',
    authenticateToken,
    async (req, res) => {
        try {
            await fetchAccessToken();
            res.json({
                success: true,
                message: 'Access token fetched',
                user: req.user.userPrincipalName,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            res.status(500).json({
                error: 'Failed to fetch access token',
                message: error.message
            });
        }
    }
);

app.post('/api/get-chat-members',
    authenticateToken,
    validateInput(['chatId']),
    async (req, res) => {
        try {
            const { chatId } = req.body;
            const members = await getChatMembers(chatId);
            res.json({
                success: true,
                members,
                user: req.user.userPrincipalName,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error(`Chat members API error for user ${req.user.userPrincipalName}:`, error);

            let statusCode = 500;
            if (['NotFound', 'Forbidden', 'Unauthorized'].includes(error.code)) {
                statusCode = { NotFound: 404, Forbidden: 403, Unauthorized: 401 }[error.code];
            }

            res.status(statusCode).json({
                error: 'Failed to fetch chat members',
                message: error.message,
                code: error.code,
                statusCode: statusCode,
                timestamp: new Date().toISOString()
            });
        }
    }
);

app.post('/api/ensure-teams-chat-files-folder',
    authenticateToken,
    validateInput(['userObjectId']),
    async (req, res) => {
        try {
            const { userObjectId } = req.body;

            if (userObjectId !== req.user.id) {
                return res.status(403).json({
                    error: 'Forbidden',
                    message: 'You can only access your own folders'
                });
            }

            await ensureTeamsChatFilesVoiceMessagesFolder(userObjectId);
            res.json({
                success: true,
                message: 'Teams Chat Files folder ensured',
                user: req.user.userPrincipalName,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            res.status(500).json({
                error: 'Failed to ensure Teams Chat Files folder',
                message: error.message
            });
        }
    }
);


async function fetchAccessToken() {
    try {
        const scopes = ['https://graph.microsoft.com/.default'];
        const tokenResponse = await credential.getToken(scopes);
        appAccessToken = tokenResponse.token;
        console.log('[server.js] Application access token fetched successfully');
        return appAccessToken;
    } catch (error) {
        console.error('Error fetching access token:', error);
        throw error;
    }
}

async function createOrGetChat(senderId, recipientId) {
    if (!recipientId || recipientId === senderId) {
        throw new Error('A valid recipient ID different from the sender is required for one-on-one chats');
    }

    const chats = await appGraphClient.api('/chats')
        .filter(`chatType eq 'oneOnOne'`)
        .expand('members')
        .get();

    const existingChat = chats.value.find(chat => {
        const members = chat.members.map(m => m.userId);
        return members.includes(senderId) && members.includes(recipientId) && members.length === 2;
    });

    if (existingChat) return existingChat;

    return await appGraphClient.api('/chats').post({
        chatType: 'oneOnOne',
        members: [
            {
                '@odata.type': '#microsoft.graph.aadUserConversationMember',
                roles: ['owner'],
                '<EMAIL>': `https://graph.microsoft.com/v1.0/users('${senderId}')`
            },
            {
                '@odata.type': '#microsoft.graph.aadUserConversationMember',
                roles: ['owner'],
                '<EMAIL>': `https://graph.microsoft.com/v1.0/users('${recipientId}')`
            }
        ]
    });
}

async function getChatMembers(chatId) {
    try {
        const members = await appGraphClient.api(`/chats/${chatId}/members`).get();

        const memberDetails = await Promise.all(
            members.value.map(async (member) => {
                try {
                    const userDetails = await appGraphClient.api(`/users/${member.userId}`).get();
                    return {
                        id: member.userId,
                        userId: member.userId,
                        email: userDetails.mail || userDetails.userPrincipalName,
                        userPrincipalName: userDetails.userPrincipalName,
                        displayName: userDetails.displayName
                    };
                } catch (userError) {
                    console.warn(`Could not fetch details for user ${member.userId}:`, userError.message);
                    return {
                        id: member.userId,
                        userId: member.userId,
                        email: null,
                        userPrincipalName: null,
                        displayName: 'Unknown User'
                    };
                }
            })
        );

        return memberDetails.filter(member => member.id);
    } catch (error) {
        let userMessage = 'Failed to fetch chat members';
        const errorMessages = {
            'NotFound': 'Chat not found. This may be a private chat, an invalid ID, or the first chat without any text messages. Please start a text chat before sending voice messages.',
            'Forbidden': 'Access denied. You may not have permission to view this chat\'s members.',
            'Unauthorized': 'Authentication failed. Please try refreshing the app.'
        };

        if (error.code in errorMessages) {
            userMessage = errorMessages[error.code];
        } else if (error.message?.includes('LocationLookupFailed')) {
            userMessage = 'Chat location lookup failed. This chat may not be accessible or may have been deleted.';
        }

        const enhancedError = new Error(userMessage);
        enhancedError.originalError = error;
        enhancedError.code = error.code;
        enhancedError.statusCode = error.statusCode;
        throw enhancedError;
    }
}

async function ensureTeamsChatFilesVoiceMessagesFolder(userId) {
    const folderPath = '/Microsoft Teams Chat Files/VoiceMessages';

    try {
        const folder = await appGraphClient.api(`/users/${userId}/drive/root:${folderPath}`).get();
        await makeTeamsChatFilesVoiceMessagesFolderPrivate(userId, folder.id);
        return folder;
    } catch (error) {
        if (error.statusCode === 404) {
            // Ensure Teams Chat Files folder exists
            try {
                await appGraphClient.api(`/users/${userId}/drive/root:/Microsoft Teams Chat Files`).get();
            } catch (teamsError) {
                if (teamsError.statusCode === 404) {
                    await appGraphClient.api(`/users/${userId}/drive/root/children`).post({
                        name: 'Microsoft Teams Chat Files',
                        folder: {},
                        '@microsoft.graph.conflictBehavior': 'replace'
                    });
                }
            }

            const newFolder = await appGraphClient.api(`/users/${userId}/drive/root:/Microsoft Teams Chat Files:/children`).post({
                name: 'VoiceMessages',
                folder: {},
                '@microsoft.graph.conflictBehavior': 'replace'
            });

            await makeTeamsChatFilesVoiceMessagesFolderPrivate(userId, newFolder.id);
            return newFolder;
        }
        throw error;
    }
}

async function makeTeamsChatFilesVoiceMessagesFolderPrivate(userId, folderId) {
    try {
        // Remove existing sharing links
        const permissions = await appGraphClient.api(`/users/${userId}/drive/items/${folderId}/permissions`).get();
        await Promise.all(
            permissions.value
                .filter(permission => permission.link)
                .map(permission => 
                    appGraphClient.api(`/users/${userId}/drive/items/${folderId}/permissions/${permission.id}`)
                        .delete()
                        .catch(() => {}) // Ignore deletion errors
                )
        );

        // Set folder metadata
        await appGraphClient.api(`/users/${userId}/drive/items/${folderId}`)
            .patch({
                description: 'Private voice messages folder in Teams Chat Files - Access controlled by application',
                '@microsoft.graph.conflictBehavior': 'replace'
            })
            .catch(() => {}); // Ignore metadata update errors
    } catch (error) {
        // Ignore folder security errors
    }
}

async function convertAudioToMp3(fileBuffer) {
    const tempMp3Path = path.join(__dirname, 'temp', `${uuidv4()}.mp3`);
    await fsPromises.mkdir(path.dirname(tempMp3Path), { recursive: true });

    return new Promise((resolve, reject) => {
        const inputStream = new Readable({
            highWaterMark: 256 * 1024,
            read() {}
        });

        ffmpeg(inputStream)
            .noVideo()
            .audioCodec('libmp3lame')
            .audioBitrate(8)
            .audioChannels(1)
            .audioFrequency(8000)
            .addOutputOption('-preset', 'ultrafast', '-q:a', '9', '-compression_level', '0')
            .toFormat('mp3')
            .on('error', (err) => reject(new Error(`Audio conversion failed: ${err.message}`)))
            .on('end', () => resolve(tempMp3Path))
            .save(tempMp3Path);

        // Stream file buffer in chunks
        const chunkSize = 256 * 1024;
        for (let offset = 0; offset < fileBuffer.length; offset += chunkSize) {
            inputStream.push(fileBuffer.slice(offset, offset + chunkSize));
        }
        inputStream.push(null);
    });
}

async function uploadFileToTeams(chatId, fileName, fileBuffer, userContext, delegatedToken) {
    let tempMp3Path;
    try {
        // Convert audio
        tempMp3Path = await convertAudioToMp3(fileBuffer);
        const mp3Buffer = await fsPromises.readFile(tempMp3Path);

        // Upload to OneDrive
        const timestamp = Date.now();
        const chatHash = chatId.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 8);
        const secureFileName = `vm_${chatHash}_${timestamp}_${fileName.replace('.webm', '.mp3')}`;
        const voiceMessagesPath = `/Microsoft Teams Chat Files/VoiceMessages/${secureFileName}`;

        const driveItem = await appGraphClient.api(`/users/${userContext.userObjectId}/drive/root:${voiceMessagesPath}:/content`)
            .headers({
                'Content-Type': 'audio/mpeg',
                'Prefer': 'respond-async',
                'Content-Length': mp3Buffer.length
            })
            .put(mp3Buffer);

        const delegatedGraphClient = Client.init({
            authProvider: (done) => {
                if (!delegatedToken) {
                    done(new Error('No delegated access token provided'), null);
                    return;
                }
                done(null, delegatedToken);
            }
        });

        // Add metadata
        await delegatedGraphClient.api(`/me/drive/items/${driveItem.id}`)
            .patch({
                description: `Voice message | Sender: ${userContext.userObjectId} | Chat: ${chatId} | Created: ${new Date().toISOString()}`,
                '@microsoft.graph.conflictBehavior': 'replace'
            })
            .catch(() => {}); // Ignore metadata errors

        // Create sharing link
        const sharingLink = await delegatedGraphClient.api(`/me/drive/items/${driveItem.id}/createLink`)
            .post({
                type: 'view',
                scope: 'organization'
            });

        const audioUrl = sharingLink.link.webUrl;

        // Send message to chat
        if (chatId && delegatedToken) {
            const adaptiveCard = {
                type: 'AdaptiveCard',
                version: '1.4',
                body: [
                    {
                        type: 'TextBlock',
                        weight: 'Bolder',
                        size: 'Medium'
                    },
                    {
                        type: 'Media',
                        sources: [{
                            mimeType: 'audio/mpeg',
                            url: audioUrl
                        }]
                    }
                ]
            };

            await delegatedGraphClient.api(`/chats/${chatId}/messages`)
                .post({
                    body: {
                        contentType: 'html',
                        content: `<attachment id="${driveItem.id}"></attachment>`
                    },
                    attachments: [{
                        id: driveItem.id,
                        contentType: 'application/vnd.microsoft.card.adaptive',
                        content: JSON.stringify(adaptiveCard)
                    }]
                });
        }

        return {
            id: driveItem?.id,
            downloadUrl: audioUrl,
            fileName
        };
    } catch (error) {
        console.error('Error in uploadFileToTeams:', error);
        
        if (error.message.includes('conversion')) {
            throw new Error('Audio conversion failed. Please try recording your voice message again.');
        } else if (error.message.includes('OneDrive') || error.message.includes('drive')) {
            throw new Error('Failed to save voice message to OneDrive. Please check your connection and try again.');
        } else if (error.message.includes('sharing')) {
            throw new Error('Failed to create secure sharing link. Please try again.');
        } else if (error.message.includes('timeout')) {
            throw new Error('Upload timed out. Please try again with a shorter voice message.');
        } else {
            throw new Error(`Voice message processing failed: ${error.message}`);
        }
    } finally {
        if (tempMp3Path) {
            await fsPromises.unlink(tempMp3Path).catch(() => {});
        }
    }
}

// Socket.IO connection handling
io.on('connection', (socket) => {
    let userContext = null;

    socket.on('initialize-context', (context) => {
        userContext = context;
    });

    socket.on('upload-voice-message', async (data) => {
        let timeoutId;
        try {
            if (!userContext) {
                throw new Error('User context not initialized. Please try again.');
            }

            const { audioBlob, recipientId, senderId, chatType, channelId, delegatedToken } = data;

            if (!delegatedToken) {
                throw new Error('Authentication required: No delegated token provided');
            }

            // Validate delegated token
            const testGraphClient = Client.init({
                authProvider: (done) => done(null, delegatedToken)
            });

            const userProfile = await testGraphClient.api('/me').get();
            if (userProfile.id !== senderId) {
                throw new Error('Authentication error: User mismatch');
            }

            let chatId = channelId || null;
            if (!chatId && chatType === 'oneOnOne' && recipientId && senderId !== recipientId) {
                const chat = await createOrGetChat(senderId, recipientId);
                chatId = chat.id;
            }

            if (!chatId) {
                throw new Error('Chat ID is required for sending messages');
            }

            if (!audioBlob || !senderId) {
                throw new Error('Missing required fields: audioBlob or senderId');
            }

            const messageId = uuidv4();
            const fileName = `voice-message-${messageId}.webm`;
            const buffer = Buffer.from(audioBlob, 'base64');

            // Set timeout
            timeoutId = setTimeout(() => {
                socket.emit('upload-error', {
                    error: 'Voice message processing timed out. The app will close automatically.',
                    details: 'Operation took too long to complete (60 seconds)',
                    shouldClose: true
                });

                setTimeout(() => {
                    socket.emit('closeApp', {
                        reason: 'Processing timeout - operation exceeded time limit',
                        error: 'Timeout after 60 seconds'
                    });
                }, 2000);
            }, 60000);

            const uploadedFile = await uploadFileToTeams(chatId, fileName, buffer, userContext, delegatedToken, data.preFetchedData);

            clearTimeout(timeoutId);

            socket.emit('upload-success', {
                messageId,
                downloadUrl: uploadedFile.downloadUrl,
                fileName: uploadedFile.fileName,
                chatId,
                userId: userContext?.userObjectId || 'unknown',
                userName: userContext?.userDisplayName || 'Unknown User'
            });

        } catch (error) {
            console.error('Error handling voice message:', error);

            if (timeoutId) clearTimeout(timeoutId);

            const isCriticalError = error.message.includes('Authentication') ||
                                  error.message.includes('Socket connection lost') ||
                                  error.message.includes('User context not initialized');

            socket.emit('upload-error', {
                error: isCriticalError ?
                    'Failed to send voice message. The app will close automatically.' :
                    'Failed to send voice message. Please try again.',
                details: error.message,
                shouldClose: isCriticalError
            });

            if (isCriticalError) {
                setTimeout(() => {
                    socket.emit('closeApp', {
                        reason: 'Critical error occurred during voice message processing',
                        error: error.message
                    });
                }, 2000);
            }
        }
    });

    socket.on('error', (error) => {
        console.error('Socket.IO error:', error);
    });
});

// Cleanup interval
setInterval(() => {
    if (global.gc) global.gc();
}, 300000);

// Start server
server.listen(config.port, () => {
    console.log(`Server running on https://localhost:${config.port}`);
});