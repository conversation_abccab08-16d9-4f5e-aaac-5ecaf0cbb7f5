<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Message Recorder</title>
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="config.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            overflow: hidden;
            margin: 0;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .app-container {
            position: relative;
            z-index: 2;
            width: 334px;
            height: 364px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 16px;
            box-shadow:
                0 16px 32px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }

            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .app-header {
            text-align: center;
            margin-bottom: 12px;
            flex-shrink: 0;
        }

        .app-title {
            font-size: 18px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2px;
            letter-spacing: -0.5px;
        }

        .app-subtitle {
            color: #64748b;
            font-size: 12px;
            font-weight: 400;
        }

        .recording-area {
            text-align: center;
            margin: 8px 0;
            position: relative;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 140px;
        }

        .record-button-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding-top: 10px;
        }

        .record-button {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            box-shadow:
                0 12px 24px rgba(255, 107, 107, 0.3),
                0 0 0 0 rgba(255, 107, 107, 0.4);
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .record-button::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24, #ff6b6b);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .record-button:hover {
            transform: scale(1.05);
            box-shadow:
                0 25px 50px rgba(255, 107, 107, 0.4),
                0 0 0 8px rgba(255, 107, 107, 0.1);
        }

        .record-button:hover::before {
            opacity: 1;
        }

        .record-button.recording {
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            animation: recordingPulse 2s infinite;
            box-shadow:
                0 20px 40px rgba(255, 71, 87, 0.4),
                0 0 0 0 rgba(255, 71, 87, 0.6);
        }

        @keyframes recordingPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow:
                    0 20px 40px rgba(255, 71, 87, 0.4),
                    0 0 0 0 rgba(255, 71, 87, 0.6);
            }

            50% {
                transform: scale(1.08);
                box-shadow:
                    0 25px 50px rgba(255, 71, 87, 0.5),
                    0 0 0 15px rgba(255, 71, 87, 0.2);
            }
        }

        .record-icon {
            font-size: 28px;
            transition: all 0.3s ease;
        }

        .record-button.recording .record-icon {
            animation: iconBounce 1s infinite;
        }

        @keyframes iconBounce {
            0%, 100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }
        }

        .status-message {
            font-size: 14px;
            font-weight: 500;
            color: #475569;
            min-height: 18px;
            transition: all 0.3s ease;
        }

        .status-message.recording {
            display: none;
        }

        .recording-timer {
            font-size: 11px;
            color: #64748b;
            font-weight: 400;
            min-height: 14px;
        }

        .preview-section {
            margin: 8px 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            flex-shrink: 0;
            display: none;
        }

        .preview-section.show {
            opacity: 1;
            transform: translateY(0);
            display: block;
        }

        .audio-preview {
            width: 100%;
            height: 35px;
            border-radius: 10px;
            outline: none;
            margin-bottom: 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .action-btn {
            width: 42px;
            height: 42px;
            border-radius: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            position: relative;
            overflow: hidden;
        }

        .action-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .action-btn:disabled::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            z-index: 1;
        }

        .action-btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        .action-btn:disabled .tooltip {
            display: none;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .action-btn:hover::before {
            opacity: 0.1;
        }

        .btn-send {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
        }

        .btn-send:hover:not(:disabled) {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 12px 24px rgba(16, 185, 129, 0.4);
        }

        .btn-send:disabled {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            box-shadow: none;
        }

        .btn-discard {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
        }

        .btn-discard:hover:not(:disabled) {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 12px 24px rgba(239, 68, 68, 0.4);
        }

        .btn-discard:disabled {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            box-shadow: none;
        }

        .processing-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 12px;
            text-align: center;
            margin: 8px 0;
            font-weight: 600;
            font-size: 12px;
            box-shadow: 0 6px 12px rgba(245, 158, 11, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            flex-shrink: 0;
        }

        .processing-warning.show {
            opacity: 1;
            transform: translateY(0);
        }

        .tooltip {
            position: absolute;
            bottom: -32px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .tooltip::before {
            content: '';
            position: absolute;
            top: -4px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid rgba(0, 0, 0, 0.8);
        }

        .action-btn:hover .tooltip {
            opacity: 1;
            transform: translateX(-50%) translateY(-4px);
        }

        .wave-animation {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 24px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .recording-area.recording .wave-animation {
            opacity: 1;
        }

        .wave {
            width: 2px;
            height: 12px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            margin: 0 1px;
            border-radius: 1px;
            display: inline-block;
            animation: wave 1.2s infinite ease-in-out;
        }

        .wave:nth-child(2) {
            animation-delay: -1.1s;
        }

        .wave:nth-child(3) {
            animation-delay: -1.0s;
        }

        .wave:nth-child(4) {
            animation-delay: -0.9s;
        }

        .wave:nth-child(5) {
            animation-delay: -0.8s;
        }

        .wave:nth-child(6) {
            animation-delay: -0.7s;
        }

        .wave:nth-child(7) {
            animation-delay: -0.6s;
        }

        .wave:nth-child(8) {
            animation-delay: -0.5s;
        }

        @keyframes wave {
            0%, 40%, 100% {
                transform: scaleY(0.4);
                opacity: 0.5;
            }

            20% {
                transform: scaleY(1);
                opacity: 1;
            }
        }

        .status-alert {
            border-radius: 10px;
            border: none;
            padding: 8px 12px;
            font-weight: 500;
            font-size: 12px;
            margin: 6px 0;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .status-alert.show {
            opacity: 1;
            transform: translateY(0);
        }

        .status-alert.alert-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 8px 16px rgba(16, 185, 129, 0.2);
        }

        .status-alert.alert-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 8px 16px rgba(239, 68, 68, 0.2);
        }

        @media (max-width: 480px) {
            .app-container {
                width: 320px;
                height: 350px;
                padding: 12px;
            }

            .record-button {
                width: 70px;
                height: 70px;
            }

            .record-icon {
                font-size: 24px;
            }

            .action-btn {
                width: 38px;
                height: 38px;
                font-size: 14px;
            }

            .app-title {
                font-size: 16px;
            }

            .app-subtitle {
                font-size: 11px;
            }
        }
    </style>
</head>

<body>
    <div class="app-container">
        <div class="app-header">
            <h1 class="app-title">🎤 Voice Message</h1>
            <p class="app-subtitle">Record and send voice messages instantly</p>
        </div>

        <div class="recording-area" id="recordingArea">
            <div class="record-button-container">
                <button id="recordButton" class="record-button">
                    <i class="fas fa-microphone record-icon"></i>
                </button>

                <div class="wave-animation">
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                </div>
            </div>

            <div class="status-info">
                <div class="status-message" id="statusMessage">Tap to start recording</div>
                <div class="recording-timer" id="recordingTimer"></div>
            </div>
        </div>

        <div class="preview-section" id="previewSection">
            <audio id="preview" class="audio-preview" controls></audio>

            <div class="action-buttons" id="previewControls">
                <button id="sendButton" class="action-btn btn-send">
                    <i class="fas fa-paper-plane"></i>
                    <div class="tooltip">Send Message</div>
                </button>
                <button id="discardButton" class="action-btn btn-discard">
                    <i class="fas fa-trash-alt"></i>
                    <div class="tooltip">Delete Recording</div>
                </button>
            </div>
        </div>

        <div id="processingWarning" class="processing-warning">
            <i class="fas fa-spinner fa-spin"></i> Sending voice message...
            <br><small>Please keep this window open</small>
        </div>

        <div id="statusAlert" class="status-alert"></div>
    </div>

    <script>
        console.log('[index.html] Script loaded at', new Date().toISOString());
        let mediaRecorder;
        let audioChunks = [];
        let recordingTimer;
        let recordingDuration = 0;
        let stream;
        let isSending = false;
        let hasSent = false;
        let socket = null;

        let preFetchedData = {
            accessToken: null,
            chatMembers: null,
            folderReady: false,
            userContext: null
        };

        async function initializeSocket() {
            if (socket?.connected) {
                return socket;
            }

            return new Promise((resolve, reject) => {
                try {
                    socket = io(window.APP_CONFIG.SOCKET_URL, {
                        path: '/socket.io',
                        transports: ['polling'],
                        reconnection: true,
                        reconnectionAttempts: 5,
                        reconnectionDelay: 1000,
                        timeout: 60000,
                        query: { client: 'teams', timestamp: Date.now() }
                    });

                    const connectionTimeout = setTimeout(() => {
                        if (!socket?.connected) {
                            console.error('[index.html] Socket connection timeout');
                            reject(new Error('Connection timeout - server may be unavailable'));
                        }
                    }, 10000);

                    socket.on('connect', () => {
                        console.log('[index.html] Socket.IO connected successfully');
                        clearTimeout(connectionTimeout);

                        try {
                            microsoftTeams.getContext((context) => {
                                console.log('[index.html] Initializing context after socket connection');
                                if (!context.userObjectId && !context.userId) {
                                    console.error('[index.html] No user ID in context');
                                    const error = new Error('No user ID in context - please refresh the app');
                                    showStatusMessage('Failed to retrieve user information.', false);
                                    reject(error);
                                    return;
                                }

                                try {
                                    socket.emit('initialize-context', {
                                        userObjectId: context.userObjectId || context.userId,
                                        userDisplayName: context.userPrincipalName || 'Unknown User',
                                        chatId: context.chatId || context.channelId || null
                                    });
                                    resolve(socket);
                                } catch (emitError) {
                                    console.error('[index.html] Error emitting initialize-context:', emitError);
                                    reject(new Error(`Failed to initialize context: ${emitError.message}`));
                                }
                            });
                        } catch (contextError) {
                            console.error('[index.html] Error getting Teams context:', contextError);
                            reject(new Error(`Failed to get Teams context: ${contextError.message}`));
                        }
                    });

                    socket.on('connect_error', (error) => {
                        console.error('[index.html] Socket.IO connection error:', error);
                        clearTimeout(connectionTimeout);
                        const errorMessage = error.message || error.description || 'Unknown connection error';
                        showStatusMessage(`Cannot connect to server: ${errorMessage}. Please try again.`, false);
                        reject(new Error(`Connection failed: ${errorMessage}`));
                    });

                    socket.on('disconnect', (reason) => {
                        console.log('[index.html] Socket.IO disconnected:', reason);
                        clearTimeout(connectionTimeout);
                        socket = null;

                        if (reason === 'io server disconnect') {
                            logAndShow('❌ Server disconnected. Please try again.', false, false);
                        } else if (reason === 'transport close') {
                            logAndShow('❌ Connection lost. Please check your internet connection.', false, false);
                        }
                    });

                    socket.on('error', (error) => {
                        console.error('[index.html] Socket.IO error:', error);
                        const errorMessage = error.message || error.details || 'Unknown socket error';
                        logAndShow(`❌ Connection error: ${errorMessage}`, false);
                    });

                    socket.on('upload-success', (data) => {
                        console.log('[index.html] Upload success received:', data);

                        const processingWarning = document.getElementById('processingWarning');
                        if (processingWarning) {
                            processingWarning.classList.remove('show');
                        }

                        logAndShow('✅ Voice message sent successfully!', true, false);

                        const result = {
                            messageId: data.messageId,
                            downloadUrl: data.downloadUrl,
                            fileName: data.fileName,
                            chatId: data.chatId,
                            userId: data.userId,
                            userName: data.userName,
                            status: 'complete'
                        };

                        if (stream) {
                            stream.getTracks().forEach(track => track.stop());
                            stream = null;
                        }
                        audioChunks = [];

                        console.log('[index.html] Cleaning up and closing...');

                        if (socket?.connected) {
                            socket.disconnect();
                            socket = null;
                        }

                        setTimeout(() => {
                            try {
                                microsoftTeams.tasks.submitTask(result);
                                window.close();
                            } catch (closeError) {
                                console.error('[index.html] Error closing app:', closeError);
                                window.close();
                            }
                        }, 1000);
                    });

                    socket.on('upload-error', (error) => {
                        console.error('[index.html] Upload error received:', error);

                        const processingWarning = document.getElementById('processingWarning');
                        if (processingWarning) {
                            processingWarning.classList.remove('show');
                        }

                        logAndShow(`❌ Failed to send message: ${error.details || error.message || 'Unknown upload error'}`, false, false);

                        if (socket?.connected) {
                            socket.disconnect();
                            socket = null;
                        }
                    });

                } catch (initError) {
                    console.error('[index.html] Error initializing socket:', initError);
                    reject(new Error(`Failed to initialize connection: ${initError.message}`));
                }
            });
        }

        function disableActionButtons() {
            const sendButton = document.getElementById('sendButton');
            const discardButton = document.getElementById('discardButton');
            
            if (sendButton) {
                sendButton.disabled = true;
            }
            if (discardButton) {
                discardButton.disabled = true;
            }
            
            isSending = true;
        }

        function enableActionButtons() {
            const sendButton = document.getElementById('sendButton');
            const discardButton = document.getElementById('discardButton');
            
            if (!hasSent && sendButton) {
                sendButton.disabled = false;
            }
            if (discardButton) {
                discardButton.disabled = false;
            }
            
            isSending = false;
        }

        function logAndShow(message, isSuccess = false, enableRecordButton = false) {
            console.log(`[index.html] ${message}`);
            showStatusMessage(message, isSuccess);
            const recordButton = document.getElementById('recordButton');
            if (recordButton && !enableRecordButton) {
                recordButton.disabled = true;
                recordButton.style.opacity = '0.5';
                recordButton.style.cursor = 'not-allowed';
            }
        }

        function showStatusMessage(message, isSuccess) {
            const statusAlert = document.getElementById('statusAlert');
            const statusMessage = document.getElementById('statusMessage');

            statusMessage.textContent = message;

            if (message.includes('❌') || message.includes('✅') || message.includes('⚠️')) {
                statusAlert.innerHTML = message;
                statusAlert.classList.remove('alert-success', 'alert-danger');
                statusAlert.classList.add(isSuccess ? 'alert-success' : 'alert-danger', 'show');

                setTimeout(() => {
                    statusAlert.classList.remove('show');
                }, 5000);
            }
        }

        let authCache = {
            delegatedToken: null,
            tokenExpiry: null,
            userContext: null,
            isAuthenticated: false
        };

        function detectOS() {
            const userAgent = navigator.userAgent.toLowerCase();
            if (userAgent.includes('linux') || userAgent.includes('ubuntu')) {
                return 'linux';
            } else if (userAgent.includes('win')) {
                return 'windows';
            } else if (userAgent.includes('mac')) {
                return 'mac';
            }
            return 'unknown';
        }

        function isTeamsDesktopClient() {
            const userAgent = navigator.userAgent.toLowerCase();
            return userAgent.includes('teams/') || userAgent.includes('msteams') || userAgent.includes('teams-for-linux') || userAgent.includes('electron');
        }

        function showLinuxMessage() {
            const statusMessage = document.getElementById('statusMessage');
            if (statusMessage) {
                statusMessage.innerHTML = `
                    <div style="
                        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
                        color: #92400e;
                        border: 1px solid #fcd34d;
                        border-radius: 8px;
                        padding: 8px 10px;
                        font-size: 11px;
                        font-weight: 500;
                        line-height: 1.3;
                        box-shadow: 0 2px 4px rgba(251, 191, 36, 0.2);
                        animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
                    ">
                        <div style="font-weight: 600; margin-bottom: 4px; color: #92400e;">
                            ⚠️ Ubuntu Teams-for-Linux Users
                        </div>
                        <div style="margin-bottom: 4px; color: #a16207;">
                            Popups blocked in desktop client
                        </div>
                        <a href="https://teams.microsoft.com" target="_blank"
                           style="color: #1d4ed8; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 4px; justify-content: center;">
                            🌐 Open Teams in Browser
                        </a>
                    </div>
                `;
            }

            const recordButton = document.getElementById('recordButton');
            if (recordButton) {
                recordButton.disabled = true;
                recordButton.style.opacity = '0.5';
                recordButton.style.cursor = 'not-allowed';
                recordButton.title = 'Please use Teams in browser on Ubuntu/Linux';
            }
        }

        async function preAuthenticate() {
            try {
                console.log('[index.html] 🚀 Starting pre-authentication for ultra-fast sending...');
                const os = detectOS();
                const isDesktopClient = isTeamsDesktopClient();
                if (os === 'linux' && isDesktopClient) {
                    console.log('[index.html] 🐧 Ubuntu teams-for-linux detected - bypassing pre-authentication due to popup blocking');
                    showLinuxMessage();
                    return;
                }
                const context = await new Promise((resolve) => {
                    microsoftTeams.getContext(resolve);
                });
                authCache.userContext = context;
                console.log('[index.html] ✅ User context cached');
                const sessionToken = sessionStorage.getItem('delegatedToken');
                if (sessionToken) {
                    try {
                        const response = await fetch('https://graph.microsoft.com/v1.0/me', {
                            headers: { 'Authorization': `Bearer ${sessionToken}` }
                        });
                        if (response.ok) {
                            authCache.delegatedToken = sessionToken;
                            authCache.isAuthenticated = true;
                            console.log('[index.html] ⚡ Session token is valid (checked with Graph API)');
                            return;
                        }
                    } catch (err) {
                        // Ignore and fallback to auth
                    }
                }
                return await new Promise((resolve, reject) => {
                    microsoftTeams.authentication.authenticate({
                        url: window.APP_CONFIG.getAuthUrl(),
                        width: 600,
                        height: 400,
                        successCallback: (token) => {
                            authCache.delegatedToken = token;
                            authCache.isAuthenticated = true;
                            sessionStorage.setItem('delegatedToken', token);
                            resolve(token);
                        },
                        failureCallback: reject
                    });
                });
            } catch (error) {
                console.error('[index.html] Pre-authentication failed:', error);
                authCache.isAuthenticated = false;
                const os = detectOS();
                const isDesktopClient = isTeamsDesktopClient();
                if (os === 'linux' && isDesktopClient) {
                    console.log('[index.html] 🐧 Linux popup blocked - showing browser message');
                    showLinuxMessage();
                }
            }
        }

        // Check if token is still valid
        function isTokenValid() {
            return authCache.isAuthenticated &&
                authCache.delegatedToken &&
                authCache.tokenExpiry &&
                Date.now() < authCache.tokenExpiry;
        }

        // Get valid token (refresh if needed)
        async function getValidToken() {
            if (authCache.isAuthenticated && authCache.delegatedToken) {
                try {
                    const response = await fetch('https://graph.microsoft.com/v1.0/me', {
                        headers: { 'Authorization': `Bearer ${authCache.delegatedToken}` }
                    });
                    if (response.ok) {
                        sessionStorage.setItem('delegatedToken', authCache.delegatedToken);
                        return authCache.delegatedToken;
                    }
                } catch (err) {
                    // Ignore and fallback
                }
            }
            const sessionToken = sessionStorage.getItem('delegatedToken');
            if (sessionToken) {
                try {
                    const response = await fetch('https://graph.microsoft.com/v1.0/me', {
                        headers: { 'Authorization': `Bearer ${sessionToken}` }
                    });
                    if (response.ok) {
                        authCache.delegatedToken = sessionToken;
                        authCache.isAuthenticated = true;
                        return sessionToken;
                    }
                } catch (err) {
                    // Ignore and fallback
                }
            }
            return await new Promise((resolve, reject) => {
                microsoftTeams.authentication.authenticate({
                    url: window.APP_CONFIG.getAuthUrl(),
                    width: 600,
                    height: 400,
                    successCallback: (token) => {
                        authCache.delegatedToken = token;
                        authCache.isAuthenticated = true;
                        sessionStorage.setItem('delegatedToken', token);
                        resolve(token);
                    },
                    failureCallback: reject
                });
            });
        }

        if (typeof microsoftTeams !== 'undefined') {
            try {
                microsoftTeams.initialize(async () => {
                    microsoftTeams.appInitialization.notifyAppLoaded();
                    microsoftTeams.appInitialization.notifySuccess();

                    await preAuthenticate();
                    requestMicrophonePermission();
                    preFetchDuringAppLoad().catch(err => {
                        console.warn('[index.html] Pre-fetch failed, will retry during send:', err);
                    });
                });
            } catch (error) {
                console.error('[index.html] Teams SDK initialization failed:', error);
                showStatusMessage('Failed to initialize Teams app. Please refresh.', false);
            }
        } else {
            console.error('[index.html] Microsoft Teams SDK not loaded');
            showStatusMessage('Teams SDK failed to load. Please refresh.', false);
        }

        async function requestMicrophonePermission() {
            let permissionResolved = false;
            let permissionTimeout;
            try {
                const permissionPromise = navigator.mediaDevices.getUserMedia({ audio: true })
                    .then((mediaStream) => {
                        permissionResolved = true;
                        clearTimeout(permissionTimeout);
                        stream = mediaStream;
                        return true;
                    })
                    .catch((error) => {
                        permissionResolved = true;
                        clearTimeout(permissionTimeout);
                        console.error('[index.html] Microphone permission error:', error);
                        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                            logAndShow('❌ Microphone access denied. Please enable microphone access: Teams Settings → Apps → Citrus Voice Message → Permissions → Allow microphone', false, false);
                        } else if (error.name === 'NotFoundError') {
                            logAndShow('❌ No microphone found. Please connect a microphone and try again.', false, false);
                        } else if (error.name === 'NotReadableError') {
                            logAndShow('❌ Microphone is being used by another application. Please close other apps and try again.', false, false);
                        } else {
                            logAndShow(`❌ Microphone access failed: ${error.message}. Please check your device settings.`, false, false);
                        }
                        return false;
                    });
                permissionTimeout = setTimeout(() => {
                    if (!permissionResolved) {
                        logAndShow('⚠️ Waiting for microphone permission. Please enable microphone access: Teams Settings → Apps → Citrus Voice Message → Permissions → Allow microphone', false, false);
                    }
                }, 1000);
                return await permissionPromise;
            } catch (error) {
                clearTimeout(permissionTimeout);
                console.error('[index.html] Microphone permission error:', error);
                logAndShow(`❌ Microphone access failed: ${error.message}. Please check your device settings.`, false, false);
                return false;
            }
        }

        const recordButton = document.getElementById('recordButton');
        const recordingArea = document.getElementById('recordingArea');
        const previewSection = document.getElementById('previewSection');

        if (recordButton) {
            recordButton.addEventListener('click', async () => {
                try {
                    const os = detectOS();
                    const isDesktopClient = isTeamsDesktopClient();

                    if (os === 'linux' && isDesktopClient) {
                        console.log('[index.html] 🐧 Ubuntu teams-for-linux detected - recording blocked due to popup restrictions');
                        logAndShow('⚠️ Please use Teams in your web browser on Ubuntu/Linux', false, false);
                        return;
                    }

                    if (mediaRecorder && mediaRecorder.state === 'recording') {
                        try {
                            mediaRecorder.stop();
                            clearInterval(recordingTimer);

                            recordButton.classList.remove('recording');
                            recordButton.innerHTML = '<i class="fas fa-microphone record-icon"></i>';
                            recordingArea.classList.remove('recording');
                            document.getElementById('statusMessage').classList.remove('recording');
                            document.getElementById('statusMessage').textContent = 'Recording complete';
                            document.getElementById('recordingTimer').textContent = '';

                            // Explicitly ensure preview section is visible
                            if (audioChunks.length > 0) {
                                previewSection.classList.add('show');
                                previewSection.style.display = 'block';
                            } else {
                                logAndShow('❌ No audio data recorded. Please try again.', false, true);
                                resetRecordingUI();
                            }
                        } catch (error) {
                            console.error('[index.html] Error stopping recording:', error);
                            logAndShow(`❌ Error stopping recording: ${error.message}`, false, false);
                            resetRecordingUI();
                        }
                    } else {
                        if (!stream) {
                            const permissionGranted = await requestMicrophonePermission();
                            if (!permissionGranted) {
                                logAndShow('❌ Microphone permission required to record', false, false);
                                return;
                            }
                        }

                        try {
                            if (!window.MediaRecorder) {
                                throw new Error('MediaRecorder not supported in this browser');
                            }

                            mediaRecorder = new MediaRecorder(stream, {
                                mimeType: 'audio/webm;codecs=opus',
                                audioBitsPerSecond: 8000
                            });
                            audioChunks = [];
                            recordingDuration = 0;

                            mediaRecorder.onerror = (event) => {
                                console.error('[index.html] MediaRecorder error:', event.error);
                                logAndShow(`❌ Recording error: ${event.error.message}`, false, false);
                                resetRecordingUI();
                                stopRecording();
                            };

                            mediaRecorder.ondataavailable = (event) => {
                                if (event.data.size > 0) {
                                    audioChunks.push(event.data);
                                }
                            };

                            mediaRecorder.onstop = () => {
                                try {
                                    if (audioChunks.length > 0) {
                                        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                                        const preview = document.getElementById('preview');
                                        preview.src = URL.createObjectURL(audioBlob);
                                        previewSection.classList.add('show');
                                        previewSection.style.display = 'block';
                                    } else {
                                        logAndShow('❌ No audio data recorded. Please try again.', false, true);
                                        resetRecordingUI();
                                    }
                                } catch (error) {
                                    console.error('[index.html] Error processing recorded audio:', error);
                                    logAndShow(`❌ Error processing audio: ${error.message}`, false, false);
                                    resetRecordingUI();
                                }
                            };

                            mediaRecorder.start(1000);

                            recordButton.classList.add('recording');
                            recordButton.innerHTML = '<i class="fas fa-stop record-icon"></i>';
                            recordingArea.classList.add('recording');
                            document.getElementById('statusMessage').classList.add('recording');
                            previewSection.classList.remove('show');
                            previewSection.style.display = 'none';

                            recordingTimer = setInterval(() => {
                                recordingDuration++;
                                updateTimerDisplay();
                                if (recordingDuration >= 300) {
                                    try {
                                        mediaRecorder.stop();
                                        clearInterval(recordingTimer);
                                        logAndShow('Recording stopped (max duration reached)', false, false);
                                    } catch (error) {
                                        console.error('[index.html] Error stopping recording at max duration:', error);
                                        logAndShow(`❌ Error stopping recording: ${error.message}`, false, false);
                                        resetRecordingUI();
                                    }
                                }
                            }, 1000);

                            try {
                                await preAuthenticate();
                            } catch (error) {
                                console.warn('[index.html] Pre-authentication failed during recording:', error);
                            }

                        } catch (error) {
                            console.error('[index.html] Error starting recording:', error);
                            logAndShow(`❌ Cannot start recording: ${error.message}`, false, false);
                            resetRecordingUI();
                        }
                    }
                } catch (error) {
                    console.error('[index.html] Unexpected error in record button handler:', error);
                    logAndShow(`❌ Unexpected error: ${error.message}`, false, false);
                    resetRecordingUI();
                }
            });
        }

        function resetRecordingUI() {
            try {
                if (recordingTimer) {
                    clearInterval(recordingTimer);
                    recordingTimer = null;
                }

                if (recordButton) {
                    recordButton.classList.remove('recording');
                    recordButton.innerHTML = '<i class="fas fa-microphone record-icon"></i>';
                    recordButton.disabled = false;
                    recordButton.style.opacity = '1';
                    recordButton.style.cursor = 'pointer';
                }

                if (recordingArea) {
                    recordingArea.classList.remove('recording');
                }

                const statusMessage = document.getElementById('statusMessage');
                if (statusMessage) {
                    statusMessage.classList.remove('recording');
                    statusMessage.textContent = 'Tap to start recording';
                }

                const recordingTimerEl = document.getElementById('recordingTimer');
                if (recordingTimerEl) {
                    recordingTimerEl.textContent = '';
                }

                if (previewSection) {
                    previewSection.classList.remove('show');
                    previewSection.style.display = 'none';
                }

                recordingDuration = 0;
                audioChunks = [];
                hasSent = false;
                const sendButton = document.getElementById('sendButton');
                if (sendButton) {
                    sendButton.disabled = false;
                }
            } catch (error) {
                console.error('[index.html] Error resetting recording UI:', error);
            }
        }

        function stopRecording() {
            try {
                if (mediaRecorder && mediaRecorder.state === 'recording') {
                    mediaRecorder.stop();
                }
                if (recordingTimer) {
                    clearInterval(recordingTimer);
                    recordingTimer = null;
                }
            } catch (error) {
                console.error('[index.html] Error stopping recording:', error);
            }
        }

        function updateTimerDisplay() {
            const minutes = Math.floor(recordingDuration / 60);
            const seconds = recordingDuration % 60;
            const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('recordingTimer').textContent = timeString;
        }

        document.getElementById('sendButton').addEventListener('click', async () => {
            if (hasSent) return;
            hasSent = true;
            disableActionButtons();
            preAuthenticate();
            try {
                if (audioChunks.length === 0) {
                    logAndShow('Please record a message first', true, false);
                    hasSent = false;
                    enableActionButtons();
                    return;
                }
                logAndShow('Sending voice message...', false, false);

                try {
                    socket = await initializeSocket();
                } catch (error) {
                    logAndShow(`❌ Cannot connect to server: ${error.message}. Please try again.`, false, false);
                    hasSent = false;
                    enableActionButtons();
                    return;
                }

                const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });

                let delegatedToken;
                try {
                    delegatedToken = await getValidToken();
                    console.log('[index.html] ⚡ Using cached authentication - ZERO delay!');
                } catch (error) {
                    console.error('[index.html] Cached token failed, falling back to fresh auth:', error.message);
                    logAndShow(`❌ Authentication failed: ${error.message}`, false, false);
                    hasSent = false;
                    enableActionButtons();
                    return;
                }

                if (!delegatedToken) {
                    logAndShow('❌ No authentication token available', false, false);
                    hasSent = false;
                    enableActionButtons();
                    return;
                }

                const processingWarning = document.getElementById('processingWarning');
                processingWarning.classList.add('show');

                const handleSocketError = (error) => {
                    console.error('[index.html] Socket error during send:', error);
                    logAndShow(`❌ Connection error: ${error.message || error.details || 'Unknown error'}`, false, false);
                    cleanupSendOperation();
                };

                const handleUploadError = (error) => {
                    console.error('[index.html] Upload error:', error);
                    logAndShow(`❌ Failed to send message: ${error.details || error.message || 'Unknown error'}`, false, false);
                    cleanupSendOperation();
                };

                const cleanupSendOperation = () => {
                    if (processingWarning) {
                        processingWarning.classList.remove('show');
                    }
                    const discardButton = document.getElementById('discardButton');
                    if (discardButton) {
                        discardButton.disabled = false;
                    }
                    if (socket) {
                        socket.off('error', handleSocketError);
                        socket.off('upload-error', handleUploadError);
                        if (socket.connected) {
                            socket.disconnect();
                        }
                    }
                };

                socket.on('error', handleSocketError);
                socket.on('upload-error', handleUploadError);

                const reader = new FileReader();
                reader.onerror = () => {
                    logAndShow('❌ Failed to read audio file', false, false);
                    cleanupSendOperation();
                };

                reader.onloadend = async () => {
                    try {
                        const base64Audio = reader.result.split(',')[1];
                        const context = authCache.userContext || preFetchedData.userContext;
                        if (!context) {
                            throw new Error('No user context available - please refresh the app');
                        }

                        const payload = {
                            audioBlob: base64Audio,
                            senderId: context.userObjectId || context.userId,
                            recipientId: null,
                            chatType: context.chatId ? 'oneOnOne' : 'channel',
                            teamId: context.teamId,
                            channelId: context.chatId || context.channelId,
                            delegatedToken: delegatedToken,
                            preFetchedData: {
                                chatMembers: preFetchedData.chatMembers,
                                folderReady: preFetchedData.folderReady,
                            }
                        };

                        socket.emit('upload-voice-message', payload);
                    } catch (error) {
                        console.error('[index.html] Error in reader.onloadend:', error);
                        logAndShow(`❌ Failed to process audio: ${error.message}`, false, false);
                        cleanupSendOperation();
                    }
                };

                reader.readAsDataURL(audioBlob);

            } catch (error) {
                console.error('[index.html] Error processing voice message:', error);
                logAndShow(`❌ Failed to send message: ${error.message}`, false, false);
                const processingWarning = document.getElementById('processingWarning');
                if (processingWarning) {
                    processingWarning.classList.remove('show');
                }
                const discardButton = document.getElementById('discardButton');
                if (discardButton) {
                    discardButton.disabled = false;
                }
                if (socket?.connected) {
                    socket.disconnect();
                }
            }
        });

        document.getElementById('discardButton').addEventListener('click', () => {
            audioChunks = [];
            previewSection.classList.remove('show');
            previewSection.style.display = 'none';
            document.getElementById('statusMessage').classList.remove('recording');
            document.getElementById('statusMessage').textContent = 'Tap to start recording';
            document.getElementById('recordingTimer').textContent = '';
            logAndShow('Recording discarded', false, true);
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            hasSent = false;
            const sendButton = document.getElementById('sendButton');
            if (sendButton) {
                sendButton.disabled = false;
            }
        });

        function stopRecordingAndShowError(message) {
            console.warn('[index.html] Stopping recording due to error:', message);
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                try {
                    mediaRecorder.stop();
                } catch (err) {
                    console.error('[index.html] Error stopping MediaRecorder:', err);
                }
            }
            if (recordingTimer) {
                clearInterval(recordingTimer);
                recordingTimer = null;
            }
            if (recordButton) {
                recordButton.classList.remove('recording');
                recordButton.innerHTML = '<i class="fas fa-microphone record-icon"></i>';
            }

            if (recordingArea) {
                recordingArea.classList.remove('recording');
            }

            if (previewSection) {
                previewSection.classList.remove('show');
                previewSection.style.display = 'none';
            }
            const statusMessage = document.getElementById('statusMessage');
            if (statusMessage) {
                statusMessage.classList.remove('recording');
                statusMessage.textContent = 'Recording stopped due to an error';
            }
            const recordingTimerEl = document.getElementById('recordingTimer');
            if (recordingTimerEl) {
                recordingTimerEl.textContent = '';
            }
            audioChunks = [];
            recordingDuration = 0;
            logAndShow(` ${message}`, false, false);
        }

        async function preFetchDuringAppLoad() {
            try {
                console.log('[index.html] 🚀 Starting immediate pre-fetch operations on app load...');

                const context = authCache.userContext;
                if (!context) {
                    console.warn('[index.html] No user context available for pre-fetch');
                    logAndShow('⚠️ User context not available - some features may be slower', false, false);
                    return;
                }

                const delegatedToken = authCache.delegatedToken;
                if (!delegatedToken) {
                    console.warn('[index.html] No delegated token available for API calls');
                    logAndShow('⚠️ Authentication not ready - some features may be slower', false, false);
                    return;
                }

                const authHeaders = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${delegatedToken}`
                };

                const accessTokenPromise = fetch('/api/get-access-token', {
                    method: 'POST',
                    headers: authHeaders,
                    body: JSON.stringify({
                        chatId: context.chatId || context.channelId,
                        userObjectId: context.userObjectId || context.userId
                    })
                }).then(async response => {
                    if (!response.ok) {
                        const errorData = await response.json();
                        const errMsg = `❌ Access token API error: ${errorData.message || response.statusText} : ${errorData.statusCode || ''}`;
                        stopRecordingAndShowError(errMsg);
                        throw new Error(errMsg);
                    }
                    return response.json();
                });

                const chatMembersPromise = fetch('/api/get-chat-members', {
                    method: 'POST',
                    headers: authHeaders,
                    body: JSON.stringify({
                        chatId: context.chatId || context.channelId
                    })
                }).then(async response => {
                    if (!response.ok) {
                        const errorData = await response.json();
                        const errMsg = `❌ Chat members API error: ${errorData.message || response.statusText} : ${errorData.statusCode || ''}`;
                        stopRecordingAndShowError(errMsg);
                        throw new Error(errMsg);
                    }
                    return response.json();
                });

                const folderPromise = fetch('/api/ensure-teams-chat-files-folder', {
                    method: 'POST',
                    headers: authHeaders,
                    body: JSON.stringify({
                        userObjectId: context.userObjectId || context.userId
                    })
                }).then(async response => {
                    if (!response.ok) {
                        const errorData = await response.json();
                        const errMsg = `❌ Folder API error: ${errorData.message || response.statusText} : ${errorData.statusCode || ''}`;
                        stopRecordingAndShowError(errMsg);
                        throw new Error(errMsg);
                    }
                    return response.json();
                });

                const [accessTokenResult, chatMembersResult, folderResult] = await Promise.allSettled([
                    accessTokenPromise,
                    chatMembersPromise,
                    folderPromise
                ]);

                if (accessTokenResult.status === 'fulfilled') {
                    preFetchedData.accessToken = accessTokenResult.value.accessToken;
                    console.log('[index.html] ✅ Access token pre-fetched on app load');
                }

                if (chatMembersResult.status === 'fulfilled') {
                    preFetchedData.chatMembers = chatMembersResult.value.members;
                    console.log(`[index.html] ✅ Chat members pre-fetched: ${chatMembersResult.value.members?.length || 0}`);
                }

                if (folderResult.status === 'fulfilled') {
                    preFetchedData.folderReady = true;
                    console.log('[index.html] ✅ Teams Chat Files folder pre-created');
                }

                console.log('[index.html] 🎯 ALL PRE-FETCH OPERATIONS COMPLETED');

            } catch (error) {
                console.error('[index.html] Pre-fetch error:', error);
                stopRecordingAndShowError(error.message);
            }
        }

        console.log('[index.html] Script initialization complete - ultra-fast voice messaging ready!');
    </script>
</body>

</html>